"""
Sample Data for Memory Enhanced Review Testing
"""

from typing import List, Dict, Any


# 示例对话数据
SAMPLE_CONVERSATIONS = [
    {
        "case_id": "case_001",
        "user_id": "user_123",
        "agent_id": "agent_456",
        "messages": [
            {"id": 1, "type": "USER", "msg": "你好，我忘记了登录密码"},
            {"id": 2, "type": "AGENT", "msg": "您好，我来帮您处理密码重置"},
            {"id": 3, "type": "USER", "msg": "谢谢，我的手机号是13812345678"},
            {"id": 4, "type": "AGENT", "msg": "好的，我已经为您发送了验证码"},
            {"id": 5, "type": "USER", "msg": "收到了，验证码是123456"},
            {"id": 6, "type": "AGENT", "msg": "验证成功，您可以重新设置密码了"}
        ]
    },
    {
        "case_id": "case_002", 
        "user_id": "user_123",
        "agent_id": "agent_789",
        "messages": [
            {"id": 1, "type": "USER", "msg": "我想投诉你们的服务"},
            {"id": 2, "type": "AGENT", "msg": "非常抱歉给您带来不便，请详细说明问题"},
            {"id": 3, "type": "USER", "msg": "你们的系统总是出问题，我要找你们领导"},
            {"id": 4, "type": "AGENT", "msg": "我理解您的困扰，我可以帮您联系主管"},
            {"id": 5, "type": "AGENT", "msg": "您可以加我微信详细沟通：wx123456"},
            {"id": 6, "type": "USER", "msg": "好的，我加你微信"}
        ]
    },
    {
        "case_id": "case_003",
        "user_id": "user_456", 
        "agent_id": "agent_456",
        "messages": [
            {"id": 1, "type": "USER", "msg": "请问你们公司的内部系统是什么？"},
            {"id": 2, "type": "AGENT", "msg": "抱歉，这属于公司内部信息，不便透露"},
            {"id": 3, "type": "USER", "msg": "我想了解你们的数据库结构"},
            {"id": 4, "type": "AGENT", "msg": "这些技术细节属于商业机密"},
            {"id": 5, "type": "USER", "msg": "那你们用的什么后台管理系统？"},
            {"id": 6, "type": "AGENT", "msg": "很抱歉，我无法提供这些信息"}
        ]
    },
    {
        "case_id": "case_004",
        "user_id": "user_789",
        "agent_id": "agent_123", 
        "messages": [
            {"id": 1, "type": "USER", "msg": "你好，我需要查询账户余额"},
            {"id": 2, "type": "AGENT", "msg": "您好，请提供您的账户信息"},
            {"id": 3, "type": "USER", "msg": "我的账号是123456789"},
            {"id": 4, "type": "AGENT", "msg": "好的，您的当前余额是1000元"},
            {"id": 5, "type": "USER", "msg": "谢谢"},
            {"id": 6, "type": "AGENT", "msg": "不客气，还有其他需要帮助的吗？"}
        ]
    },
    {
        "case_id": "case_005",
        "user_id": "user_123",
        "agent_id": "agent_456",
        "messages": [
            {"id": 1, "type": "USER", "msg": "我想了解你们公司的负面新闻"},
            {"id": 2, "type": "AGENT", "msg": "我们公司一直致力于提供优质服务"},
            {"id": 3, "type": "USER", "msg": "网上说你们有数据泄露事件"},
            {"id": 4, "type": "AGENT", "msg": "我们对数据安全非常重视，如有疑问可联系官方客服"},
            {"id": 5, "type": "USER", "msg": "那你们有没有被政府部门调查过？"},
            {"id": 6, "type": "AGENT", "msg": "我们严格遵守相关法律法规"}
        ]
    }
]


# 预期的审查结果示例
EXPECTED_RESULTS = {
    "case_001": {
        "key_contact": {"hit_rule": True, "values": ["13812345678"]},
        "sensitive_inquiry": [],
        "government_inquiry": {"hit_rule": False, "values": []},
        "internal_system": {"hit_rule": False, "values": []},
        "sensitive_reply": []
    },
    "case_002": {
        "key_contact": {"hit_rule": True, "values": ["wx123456"]},
        "sensitive_inquiry": [],
        "government_inquiry": {"hit_rule": False, "values": []},
        "internal_system": {"hit_rule": False, "values": []},
        "sensitive_reply": [{"hit_rule": True, "type": "联系方式泄露", "values": ["wx123456"]}]
    },
    "case_003": {
        "key_contact": {"hit_rule": False, "values": []},
        "sensitive_inquiry": [
            {"hit_rule": True, "type": "咨询公司信息", "values": ["内部系统", "数据库结构", "后台管理系统"]}
        ],
        "government_inquiry": {"hit_rule": False, "values": []},
        "internal_system": {"hit_rule": True, "values": ["内部系统", "数据库", "后台"]},
        "sensitive_reply": []
    },
    "case_004": {
        "key_contact": {"hit_rule": False, "values": []},
        "sensitive_inquiry": [],
        "government_inquiry": {"hit_rule": False, "values": []},
        "internal_system": {"hit_rule": False, "values": []},
        "sensitive_reply": []
    },
    "case_005": {
        "key_contact": {"hit_rule": False, "values": []},
        "sensitive_inquiry": [
            {"hit_rule": True, "type": "负面新闻", "values": ["负面新闻", "数据泄露"]},
            {"hit_rule": True, "type": "政府调查", "values": ["政府部门调查"]}
        ],
        "government_inquiry": {"hit_rule": True, "values": ["政府部门调查"]},
        "internal_system": {"hit_rule": False, "values": []},
        "sensitive_reply": []
    }
}


def get_sample_conversation(case_id: str) -> Dict[str, Any]:
    """获取指定案例的示例对话"""
    for conv in SAMPLE_CONVERSATIONS:
        if conv["case_id"] == case_id:
            return conv
    return None


def get_all_sample_conversations() -> List[Dict[str, Any]]:
    """获取所有示例对话"""
    return SAMPLE_CONVERSATIONS.copy()


def get_expected_result(case_id: str) -> Dict[str, Any]:
    """获取指定案例的预期结果"""
    return EXPECTED_RESULTS.get(case_id, {})


def get_user_conversations(user_id: str) -> List[Dict[str, Any]]:
    """获取指定用户的所有对话"""
    return [conv for conv in SAMPLE_CONVERSATIONS if conv["user_id"] == user_id]


def get_agent_conversations(agent_id: str) -> List[Dict[str, Any]]:
    """获取指定客服的所有对话"""
    return [conv for conv in SAMPLE_CONVERSATIONS if conv["agent_id"] == agent_id]


# 测试配置
TEST_CONFIG = {
    "memory_enabled": True,
    "compression_enabled": True,
    "pattern_learning_enabled": True,
    "risk_scoring_enabled": True,
    "max_context_tokens": 4000,
    "memory_compression_ratio": 0.3,
    "memory_similarity_threshold": 0.8
}


# 性能基准数据
PERFORMANCE_BENCHMARKS = {
    "traditional_review": {
        "avg_processing_time": 2.5,  # seconds
        "avg_token_usage": 8000,
        "accuracy": 0.85,
        "false_positive_rate": 0.15,
        "false_negative_rate": 0.10
    },
    "memory_enhanced_target": {
        "avg_processing_time": 1.8,  # 28% improvement
        "avg_token_usage": 5600,     # 30% reduction
        "accuracy": 0.90,            # 5% improvement
        "false_positive_rate": 0.10, # 33% reduction
        "false_negative_rate": 0.08  # 20% reduction
    }
}
