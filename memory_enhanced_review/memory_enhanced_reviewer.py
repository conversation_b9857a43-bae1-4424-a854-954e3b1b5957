"""
Memory Enhanced Reviewer

基于记忆的智能红线审查器
"""

import json
import time
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

from .memory_manager import MemoryManager, ReviewMemory
from .config import MemoryConfig
from dc_ai_red_line_review.main_pipe import BasicPipeline
from dc_ai_red_line_review.utils import get_logger, get_token_count


class MemoryEnhancedReviewer:
    """记忆增强审查器"""
    
    def __init__(self, config: MemoryConfig = None):
        self.config = config or MemoryConfig.from_env()
        self.logger = get_logger(module_name="memory_enhanced_reviewer")
        
        # Initialize components
        self.memory_manager = MemoryManager(self.config)
        self.basic_pipeline = BasicPipeline()
        
        self.logger.info("Memory Enhanced Reviewer initialized")
    
    def enhanced_review(self, 
                       messages: List[Dict[str, Any]], 
                       case_id: str,
                       user_id: Optional[str] = None,
                       agent_id: Optional[str] = None) -> Dict[str, Any]:
        """执行记忆增强的审查"""
        
        start_time = time.time()
        self.logger.info(f"Starting enhanced review for case {case_id}")
        
        try:
            # Step 1: Prepare content
            formatted_content = self._format_messages(messages)
            content_tokens = get_token_count(formatted_content)
            
            # Step 2: Get relevant historical memories
            relevant_memories = self.memory_manager.get_relevant_memories(
                formatted_content, user_id=user_id
            )
            
            # Step 3: Get user risk profile
            user_profile = self.memory_manager.get_user_risk_profile(user_id) if user_id else {}
            
            # Step 4: Decide review strategy based on memory
            review_strategy = self._determine_review_strategy(
                formatted_content, relevant_memories, user_profile, content_tokens
            )
            
            self.logger.info(f"Review strategy: {review_strategy['strategy']}")
            
            # Step 5: Execute review based on strategy
            if review_strategy["strategy"] == "full_review":
                review_results = self._execute_full_review(messages, case_id)
            elif review_strategy["strategy"] == "memory_assisted":
                review_results = self._execute_memory_assisted_review(
                    messages, case_id, relevant_memories, user_profile
                )
            elif review_strategy["strategy"] == "quick_check":
                review_results = self._execute_quick_check(
                    messages, case_id, relevant_memories
                )
            else:
                # Fallback to basic review
                review_results = self.basic_pipeline.run_sync_with_chunking(messages, case_id)
            
            # Step 6: Calculate risk score and extract patterns
            risk_score = self._calculate_risk_score(review_results, user_profile)
            sensitive_patterns = self._extract_sensitive_patterns(review_results)
            
            # Step 7: Store review memory
            if self.config.enable_pattern_learning:
                review_memory = ReviewMemory(
                    case_id=case_id,
                    user_id=user_id,
                    agent_id=agent_id,
                    review_results=review_results,
                    sensitive_patterns=sensitive_patterns,
                    risk_score=risk_score,
                    timestamp=datetime.now(),
                    context_summary=self._create_context_summary(formatted_content, review_results)
                )
                self.memory_manager.store_review_memory(review_memory)
            
            # Step 8: Enhance results with memory insights
            enhanced_results = self._enhance_results_with_memory(
                review_results, relevant_memories, user_profile, review_strategy
            )
            
            total_time = time.time() - start_time
            self.logger.info(f"Enhanced review completed in {total_time:.2f}s")
            
            return {
                "id": case_id,
                "review_res": enhanced_results,
                "memory_insights": {
                    "relevant_memories_count": len(relevant_memories),
                    "user_risk_profile": user_profile,
                    "review_strategy": review_strategy,
                    "risk_score": risk_score,
                    "sensitive_patterns": sensitive_patterns,
                    "processing_time": total_time
                }
            }
            
        except Exception as e:
            self.logger.error(f"Enhanced review failed for case {case_id}: {e}")
            # Fallback to basic review
            return self.basic_pipeline.run_sync_with_chunking(messages, case_id)
    
    def _format_messages(self, messages: List[Dict[str, Any]]) -> str:
        """格式化消息为文本"""
        formatted_parts = []
        for msg in messages:
            formatted_parts.append(f"<|im_start|>{msg['type']}\n{msg['msg']}<|im_end|>")
        return "\n".join(formatted_parts)
    
    def _determine_review_strategy(self, 
                                 content: str, 
                                 memories: List[Dict], 
                                 user_profile: Dict,
                                 content_tokens: int) -> Dict[str, Any]:
        """根据记忆和用户档案确定审查策略"""
        
        # Default strategy
        strategy = {
            "strategy": "full_review",
            "confidence": 0.5,
            "reasoning": "Default full review"
        }
        
        try:
            # Factor 1: Content length
            if content_tokens > self.config.max_context_tokens:
                if memories:
                    strategy = {
                        "strategy": "memory_assisted",
                        "confidence": 0.8,
                        "reasoning": "Long content with relevant memories available"
                    }
                else:
                    strategy = {
                        "strategy": "full_review",
                        "confidence": 0.6,
                        "reasoning": "Long content, no memories, need full review"
                    }
            
            # Factor 2: User risk profile
            avg_risk = user_profile.get("average_risk_score", 0.0)
            if avg_risk > 0.7:
                strategy = {
                    "strategy": "full_review",
                    "confidence": 0.9,
                    "reasoning": "High-risk user requires full review"
                }
            elif avg_risk < 0.3 and len(memories) > 3:
                strategy = {
                    "strategy": "quick_check",
                    "confidence": 0.7,
                    "reasoning": "Low-risk user with good history"
                }
            
            # Factor 3: Memory relevance
            if len(memories) >= 3:
                # Check if memories indicate similar safe patterns
                safe_memories = sum(1 for m in memories 
                                  if m.get("risk_score", 1.0) < 0.3)
                if safe_memories >= 2:
                    strategy = {
                        "strategy": "memory_assisted",
                        "confidence": 0.8,
                        "reasoning": "Similar safe patterns found in memory"
                    }
            
            return strategy
            
        except Exception as e:
            self.logger.error(f"Failed to determine review strategy: {e}")
            return strategy
    
    def _execute_full_review(self, messages: List[Dict], case_id: str) -> Dict[str, Any]:
        """执行完整审查"""
        self.logger.info("Executing full review")
        result = self.basic_pipeline.run_sync_with_chunking(messages, case_id)
        return result.get("review_res", {})
    
    def _execute_memory_assisted_review(self, 
                                      messages: List[Dict], 
                                      case_id: str,
                                      memories: List[Dict],
                                      user_profile: Dict) -> Dict[str, Any]:
        """执行记忆辅助审查"""
        self.logger.info("Executing memory-assisted review")
        
        # Compress content based on memory insights
        formatted_content = self._format_messages(messages)
        
        if self.config.enable_memory_compression:
            compressed_content = self.memory_manager.compress_context(
                formatted_content, self.config.memory_compression_ratio
            )
            
            # Convert back to message format for processing
            # This is simplified - in practice, you'd need more sophisticated compression
            compressed_messages = messages[:len(messages)//2]  # Simple compression
        else:
            compressed_messages = messages
        
        # Execute review on compressed content
        result = self.basic_pipeline.run_sync_with_chunking(compressed_messages, case_id)
        
        # Enhance results with memory insights
        review_res = result.get("review_res", {})
        
        # Add memory-based risk adjustments
        for category in review_res:
            if isinstance(review_res[category], list):
                for item in review_res[category]:
                    if isinstance(item, dict) and "hit_rule" in item:
                        # Adjust confidence based on memory
                        item["memory_confidence"] = self._calculate_memory_confidence(
                            item, memories, user_profile
                        )
        
        return review_res
    
    def _execute_quick_check(self, 
                           messages: List[Dict], 
                           case_id: str,
                           memories: List[Dict]) -> Dict[str, Any]:
        """执行快速检查"""
        self.logger.info("Executing quick check")
        
        # Only run key contact and basic checks
        formatted_content = self._format_messages(messages)
        
        # Use basic pipeline's core components for quick checks
        result_dict = {}
        
        # Quick key contact check
        result_dict["key_contact"] = self.basic_pipeline.core_components.key_contact_review(
            content=formatted_content, 
            risk_keywords=self.basic_pipeline.prompt_dict["key_contact"]
        )
        
        # Memory-based pattern matching
        memory_patterns = []
        for memory in memories:
            patterns = memory.get("sensitive_patterns", [])
            memory_patterns.extend(patterns)
        
        if memory_patterns:
            result_dict["memory_patterns"] = self.basic_pipeline.core_components.key_contact_review(
                content=formatted_content,
                risk_keywords=memory_patterns
            )
        
        return result_dict
    
    def _calculate_risk_score(self, review_results: Dict, user_profile: Dict) -> float:
        """计算风险分数"""
        try:
            base_score = 0.0
            total_checks = 0
            
            for category, results in review_results.items():
                if isinstance(results, list):
                    for result in results:
                        if isinstance(result, dict) and "hit_rule" in result:
                            total_checks += 1
                            if result["hit_rule"]:
                                base_score += 1.0
                elif isinstance(results, dict) and "hit_rule" in results:
                    total_checks += 1
                    if results["hit_rule"]:
                        base_score += 1.0
            
            # Normalize score
            normalized_score = base_score / max(total_checks, 1)
            
            # Adjust based on user profile
            user_risk = user_profile.get("average_risk_score", 0.5)
            adjusted_score = (normalized_score * 0.7) + (user_risk * 0.3)
            
            return min(1.0, max(0.0, adjusted_score))
            
        except Exception as e:
            self.logger.error(f"Failed to calculate risk score: {e}")
            return 0.5
    
    def _extract_sensitive_patterns(self, review_results: Dict) -> List[str]:
        """提取敏感模式"""
        patterns = []
        
        try:
            for category, results in review_results.items():
                if isinstance(results, list):
                    for result in results:
                        if isinstance(result, dict) and result.get("hit_rule"):
                            values = result.get("values", [])
                            patterns.extend(values)
                elif isinstance(results, dict) and results.get("hit_rule"):
                    values = results.get("values", [])
                    patterns.extend(values)
            
            # Remove duplicates and empty values
            unique_patterns = list(set(p for p in patterns if p and p.strip()))
            return unique_patterns
            
        except Exception as e:
            self.logger.error(f"Failed to extract sensitive patterns: {e}")
            return []
    
    def _create_context_summary(self, content: str, review_results: Dict) -> str:
        """创建上下文摘要"""
        try:
            # Simple summary - first 200 chars + review summary
            content_summary = content[:200] + "..." if len(content) > 200 else content
            
            hit_categories = []
            for category, results in review_results.items():
                if isinstance(results, list):
                    if any(r.get("hit_rule", False) for r in results if isinstance(r, dict)):
                        hit_categories.append(category)
                elif isinstance(results, dict) and results.get("hit_rule"):
                    hit_categories.append(category)
            
            summary = f"Content: {content_summary}"
            if hit_categories:
                summary += f" | Sensitive categories: {', '.join(hit_categories)}"
            
            return summary
            
        except Exception as e:
            self.logger.error(f"Failed to create context summary: {e}")
            return "Summary creation failed"
    
    def _calculate_memory_confidence(self, 
                                   item: Dict, 
                                   memories: List[Dict], 
                                   user_profile: Dict) -> float:
        """计算基于记忆的置信度"""
        try:
            if not item.get("hit_rule"):
                return 1.0  # High confidence for non-hits
            
            # Check if similar patterns were found in memories
            item_values = item.get("values", [])
            memory_matches = 0
            total_memories = len(memories)
            
            for memory in memories:
                memory_patterns = memory.get("sensitive_patterns", [])
                if any(val in memory_patterns for val in item_values):
                    memory_matches += 1
            
            # Calculate confidence based on memory consistency
            if total_memories > 0:
                memory_confidence = memory_matches / total_memories
            else:
                memory_confidence = 0.5  # Default confidence
            
            # Adjust based on user profile
            user_risk = user_profile.get("average_risk_score", 0.5)
            adjusted_confidence = (memory_confidence * 0.6) + (user_risk * 0.4)
            
            return min(1.0, max(0.0, adjusted_confidence))
            
        except Exception as e:
            self.logger.error(f"Failed to calculate memory confidence: {e}")
            return 0.5
    
    def _enhance_results_with_memory(self, 
                                   review_results: Dict,
                                   memories: List[Dict],
                                   user_profile: Dict,
                                   strategy: Dict) -> Dict[str, Any]:
        """使用记忆洞察增强结果"""
        enhanced_results = review_results.copy()
        
        try:
            # Add memory insights to each category
            for category in enhanced_results:
                if isinstance(enhanced_results[category], list):
                    for item in enhanced_results[category]:
                        if isinstance(item, dict):
                            item["memory_enhanced"] = True
                            item["strategy_used"] = strategy["strategy"]
                elif isinstance(enhanced_results[category], dict):
                    enhanced_results[category]["memory_enhanced"] = True
                    enhanced_results[category]["strategy_used"] = strategy["strategy"]
            
            # Add summary insights
            enhanced_results["memory_summary"] = {
                "relevant_memories_found": len(memories),
                "user_average_risk": user_profile.get("average_risk_score", 0.0),
                "user_total_reviews": user_profile.get("total_reviews", 0),
                "common_user_patterns": user_profile.get("common_patterns", []),
                "strategy_confidence": strategy.get("confidence", 0.5),
                "strategy_reasoning": strategy.get("reasoning", "")
            }
            
            return enhanced_results
            
        except Exception as e:
            self.logger.error(f"Failed to enhance results with memory: {e}")
            return review_results
