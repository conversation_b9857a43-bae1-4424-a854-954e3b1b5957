# Memory Enhanced Review System

基于 mem0ai 的记忆增强红线审查系统实验

## 🎯 项目目标

本实验旨在探索如何利用 mem0ai 的记忆功能来改进现有的红线审查系统，通过以下方式提升性能：

1. **记忆存储** - 存储历史审查结果和敏感内容模式
2. **智能上下文** - 利用历史记忆提供更准确的审查建议  
3. **成本优化** - 通过记忆压缩减少 token 使用
4. **个性化策略** - 根据用户历史调整审查严格程度

## 🏗️ 系统架构

```
memory_enhanced_review/
├── __init__.py              # 模块初始化
├── config.py                # 配置管理
├── memory_manager.py        # 记忆管理器
├── memory_enhanced_reviewer.py  # 记忆增强审查器
├── sample_data.py           # 示例数据
├── experiment.py            # 实验脚本
└── README.md               # 说明文档
```

## 🔧 核心组件

### 1. MemoryManager (记忆管理器)
- 与 mem0ai 交互，存储和检索记忆
- 管理用户风险档案
- 提供内容压缩功能
- 清理过期记忆

### 2. MemoryEnhancedReviewer (记忆增强审查器)
- 集成记忆功能的审查逻辑
- 智能选择审查策略：
  - `full_review`: 完整审查
  - `memory_assisted`: 记忆辅助审查
  - `quick_check`: 快速检查
- 基于历史模式调整置信度

### 3. ReviewExperiment (实验框架)
- 对比传统方法和记忆增强方法
- 性能指标分析
- 生成详细实验报告

## 🚀 快速开始

### 1. 安装依赖

```bash
# 安装 mem0ai
pip install mem0ai

# 或者使用 uv
uv add mem0ai
```

### 2. 配置环境变量

```bash
# .env 文件
MEM0_API_KEY=your_mem0_api_key
MEM0_BASE_URL=https://api.mem0.ai
MEMORY_TTL=604800  # 7 days
MAX_MEMORIES_PER_USER=100
ENABLE_MEMORY_COMPRESSION=true
ENABLE_PATTERN_LEARNING=true
MAX_CONTEXT_TOKENS=4000
MEMORY_COMPRESSION_RATIO=0.3
```

### 3. 运行实验

```python
from memory_enhanced_review.experiment import run_quick_experiment

# 运行完整实验
report = run_quick_experiment()
```

### 4. 使用记忆增强审查器

```python
from memory_enhanced_review import MemoryEnhancedReviewer, MemoryConfig

# 初始化
config = MemoryConfig.from_env()
reviewer = MemoryEnhancedReviewer(config)

# 执行审查
messages = [
    {"id": 1, "type": "USER", "msg": "我忘记密码了"},
    {"id": 2, "type": "AGENT", "msg": "我来帮您处理"}
]

result = reviewer.enhanced_review(
    messages=messages,
    case_id="case_001", 
    user_id="user_123"
)

print(result)
```

## 📊 实验结果示例

```
============================================================
MEMORY ENHANCED REVIEW EXPERIMENT SUMMARY
============================================================
Total Cases Processed: 5
Experiment Time: 2025-01-01T12:00:00

PERFORMANCE IMPROVEMENTS:
  Time Improvement: 28.0%
  Token Reduction: 30.0%
  Memory Hit Rate: 60.0%
  Avg Compression: 25.0%

SUCCESS RATES:
  Traditional Method: 5/5
  Memory Enhanced: 5/5

RECOMMENDATIONS:
  1. Memory enhanced review shows significant time improvement (>10%)
  2. Memory enhanced review achieves substantial token reduction (>20%)
  3. High memory hit rate indicates effective memory utilization
============================================================
```

## 🔍 审查策略

### 1. Full Review (完整审查)
- **触发条件**: 高风险用户、无历史记忆、复杂内容
- **特点**: 执行所有审查任务，最高准确性
- **适用场景**: 首次审查、高风险场景

### 2. Memory Assisted (记忆辅助)
- **触发条件**: 有相关历史记忆、中等风险用户
- **特点**: 基于记忆压缩内容，保持准确性
- **适用场景**: 有历史模式的用户

### 3. Quick Check (快速检查)
- **触发条件**: 低风险用户、安全历史模式
- **特点**: 只执行关键检查，最高效率
- **适用场景**: 信任用户、简单查询

## 📈 性能优化

### Token 使用优化
- **记忆压缩**: 根据历史模式压缩上下文
- **智能分块**: 只处理必要的内容片段
- **策略选择**: 根据风险等级选择合适策略

### 准确性提升
- **模式学习**: 从历史审查中学习敏感模式
- **置信度调整**: 基于记忆一致性调整结果置信度
- **用户档案**: 建立个性化风险档案

## 🔧 配置选项

```python
@dataclass
class MemoryConfig:
    # Mem0 配置
    mem0_api_key: Optional[str] = None
    mem0_base_url: Optional[str] = None
    
    # 记忆设置
    memory_ttl: int = 7 * 24 * 3600  # 7天
    max_memories_per_user: int = 100
    memory_similarity_threshold: float = 0.8
    
    # 功能开关
    enable_memory_compression: bool = True
    enable_pattern_learning: bool = True
    enable_risk_scoring: bool = True
    
    # 性能设置
    max_context_tokens: int = 4000
    memory_compression_ratio: float = 0.3
```

## 🧪 测试数据

项目包含 5 个测试案例，涵盖：
- 密码重置场景
- 投诉处理场景  
- 内部信息询问
- 正常业务查询
- 负面信息询问

每个案例都有预期的审查结果，用于验证系统准确性。

## 📝 实验报告

实验会生成详细的 JSON 报告，包含：
- 传统方法结果
- 记忆增强方法结果
- 性能对比分析
- 案例级别对比
- 改进建议

## 🚧 注意事项

1. **mem0ai 依赖**: 需要安装 mem0ai 包，如果未安装会使用模拟记忆
2. **API 配置**: 需要配置有效的 mem0ai API 密钥
3. **数据隐私**: 记忆存储涉及敏感数据，注意隐私保护
4. **性能调优**: 根据实际场景调整配置参数

## 🔮 未来改进

1. **更智能的压缩算法**
2. **多模态记忆支持**
3. **联邦学习集成**
4. **实时性能监控**
5. **自动参数调优**

## 📞 支持

如有问题或建议，请联系开发团队或提交 Issue。
