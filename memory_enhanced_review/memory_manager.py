"""
Memory Manager for Red Line Review System

使用mem0ai管理审查历史和模式记忆
"""

import json
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta

try:
    from mem0 import Memory
except ImportError:
    print("Warning: mem0 not installed. Please install with: pip install mem0ai")
    Memory = None

from .config import MemoryConfig
from dc_ai_red_line_review.utils import get_logger


@dataclass
class ReviewMemory:
    """审查记忆数据结构"""
    case_id: str
    user_id: Optional[str]
    agent_id: Optional[str]
    review_results: Dict[str, Any]
    sensitive_patterns: List[str]
    risk_score: float
    timestamp: datetime
    context_summary: str


class MemoryManager:
    """记忆管理器 - 负责与mem0ai交互"""
    
    def __init__(self, config: MemoryConfig):
        self.config = config
        self.logger = get_logger(module_name="memory_manager")
        
        # Initialize mem0 client
        if Memory is None:
            self.logger.warning("mem0 not available, using mock memory")
            self.memory_client = None
            self._mock_memories = {}
        else:
            try:
                self.memory_client = Memory(
                    api_key=config.mem0_api_key,
                    base_url=config.mem0_base_url
                )
                self.logger.info("Mem0 client initialized successfully")
            except Exception as e:
                self.logger.error(f"Failed to initialize mem0 client: {e}")
                self.memory_client = None
                self._mock_memories = {}
    
    def store_review_memory(self, review_memory: ReviewMemory) -> bool:
        """存储审查记忆"""
        try:
            memory_data = {
                "case_id": review_memory.case_id,
                "user_id": review_memory.user_id,
                "agent_id": review_memory.agent_id,
                "review_results": review_memory.review_results,
                "sensitive_patterns": review_memory.sensitive_patterns,
                "risk_score": review_memory.risk_score,
                "timestamp": review_memory.timestamp.isoformat(),
                "context_summary": review_memory.context_summary
            }
            
            if self.memory_client:
                # Use mem0 to store memory
                self.memory_client.add(
                    messages=[{
                        "role": "system",
                        "content": f"Review case {review_memory.case_id}: {review_memory.context_summary}"
                    }],
                    user_id=review_memory.user_id or "system",
                    metadata={
                        "type": "review_memory",
                        "case_id": review_memory.case_id,
                        "risk_score": review_memory.risk_score,
                        "sensitive_patterns": json.dumps(review_memory.sensitive_patterns),
                        "review_results": json.dumps(review_memory.review_results)
                    }
                )
            else:
                # Mock storage
                key = f"{review_memory.user_id or 'system'}_{review_memory.case_id}"
                self._mock_memories[key] = memory_data
            
            self.logger.info(f"Stored review memory for case {review_memory.case_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to store review memory: {e}")
            return False
    
    def get_relevant_memories(self, 
                            current_content: str, 
                            user_id: Optional[str] = None,
                            limit: int = 5) -> List[Dict[str, Any]]:
        """获取相关的历史记忆"""
        try:
            if self.memory_client:
                # Use mem0 to search relevant memories
                memories = self.memory_client.search(
                    query=current_content,
                    user_id=user_id or "system",
                    limit=limit
                )
                return [memory for memory in memories if memory.get("metadata", {}).get("type") == "review_memory"]
            else:
                # Mock search
                relevant = []
                for key, memory in self._mock_memories.items():
                    if user_id and not key.startswith(f"{user_id}_"):
                        continue
                    # Simple keyword matching for mock
                    if any(pattern.lower() in current_content.lower() 
                          for pattern in memory.get("sensitive_patterns", [])):
                        relevant.append(memory)
                return relevant[:limit]
                
        except Exception as e:
            self.logger.error(f"Failed to get relevant memories: {e}")
            return []
    
    def get_user_risk_profile(self, user_id: str) -> Dict[str, Any]:
        """获取用户风险档案"""
        try:
            memories = self.get_relevant_memories("", user_id=user_id, limit=20)
            
            if not memories:
                return {
                    "average_risk_score": 0.0,
                    "total_reviews": 0,
                    "common_patterns": [],
                    "risk_trend": "unknown"
                }
            
            # Calculate risk profile
            risk_scores = []
            all_patterns = []
            
            for memory in memories:
                if isinstance(memory, dict):
                    risk_score = memory.get("risk_score", 0.0)
                    patterns = memory.get("sensitive_patterns", [])
                else:
                    # Handle mem0 memory object
                    metadata = getattr(memory, 'metadata', {})
                    risk_score = metadata.get("risk_score", 0.0)
                    patterns = json.loads(metadata.get("sensitive_patterns", "[]"))
                
                if isinstance(risk_score, (int, float)):
                    risk_scores.append(risk_score)
                all_patterns.extend(patterns)
            
            # Calculate statistics
            avg_risk = sum(risk_scores) / len(risk_scores) if risk_scores else 0.0
            pattern_counts = {}
            for pattern in all_patterns:
                pattern_counts[pattern] = pattern_counts.get(pattern, 0) + 1
            
            common_patterns = sorted(pattern_counts.items(), key=lambda x: x[1], reverse=True)[:5]
            
            # Determine risk trend (simplified)
            recent_scores = risk_scores[-5:] if len(risk_scores) >= 5 else risk_scores
            if len(recent_scores) >= 2:
                trend = "increasing" if recent_scores[-1] > recent_scores[0] else "decreasing"
            else:
                trend = "stable"
            
            return {
                "average_risk_score": avg_risk,
                "total_reviews": len(memories),
                "common_patterns": [pattern for pattern, count in common_patterns],
                "risk_trend": trend
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get user risk profile: {e}")
            return {
                "average_risk_score": 0.0,
                "total_reviews": 0,
                "common_patterns": [],
                "risk_trend": "unknown"
            }
    
    def compress_context(self, content: str, target_ratio: float = 0.3) -> str:
        """压缩上下文内容"""
        try:
            if self.memory_client and self.config.enable_memory_compression:
                # Use mem0's compression capabilities
                # This is a simplified example - actual implementation would depend on mem0's API
                compressed = content[:int(len(content) * target_ratio)]
                return compressed
            else:
                # Simple compression - keep important parts
                lines = content.split('\n')
                important_lines = []
                
                for line in lines:
                    # Keep lines with potential sensitive content
                    if any(keyword in line.lower() for keyword in 
                          ['微信', '电话', '密码', '账号', '投诉', '问题']):
                        important_lines.append(line)
                
                # If no important lines found, keep first and last parts
                if not important_lines:
                    total_lines = len(lines)
                    keep_count = max(1, int(total_lines * target_ratio))
                    important_lines = lines[:keep_count//2] + lines[-keep_count//2:]
                
                return '\n'.join(important_lines)
                
        except Exception as e:
            self.logger.error(f"Failed to compress context: {e}")
            return content
    
    def cleanup_old_memories(self, days: int = 7):
        """清理过期记忆"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            if self.memory_client:
                # This would depend on mem0's API for memory management
                self.logger.info("Memory cleanup would be handled by mem0's TTL settings")
            else:
                # Mock cleanup
                to_remove = []
                for key, memory in self._mock_memories.items():
                    timestamp_str = memory.get("timestamp", "")
                    if timestamp_str:
                        timestamp = datetime.fromisoformat(timestamp_str)
                        if timestamp < cutoff_date:
                            to_remove.append(key)
                
                for key in to_remove:
                    del self._mock_memories[key]
                
                self.logger.info(f"Cleaned up {len(to_remove)} old memories")
                
        except Exception as e:
            self.logger.error(f"Failed to cleanup old memories: {e}")
