"""
Memory Enhanced Review Experiment

对比传统审查方法和记忆增强审查方法的实验脚本
"""

import json
import time
import statistics
from typing import Dict, List, Any, Tuple
from datetime import datetime

from .config import MemoryConfig
from .memory_enhanced_reviewer import MemoryEnhancedReviewer
from .sample_data import (
    get_all_sample_conversations, 
    get_expected_result,
    PERFORMANCE_BENCHMARKS
)
from dc_ai_red_line_review.main_pipe import BasicPipeline
from dc_ai_red_line_review.utils import get_logger


class ReviewExperiment:
    """审查实验类"""
    
    def __init__(self, config: MemoryConfig = None):
        self.config = config or MemoryConfig.from_env()
        self.logger = get_logger(module_name="review_experiment")
        
        # Initialize reviewers
        self.traditional_reviewer = BasicPipeline()
        self.memory_reviewer = MemoryEnhancedReviewer(self.config)
        
        self.logger.info("Review experiment initialized")
    
    def run_full_experiment(self) -> Dict[str, Any]:
        """运行完整的对比实验"""
        self.logger.info("Starting full experiment")
        
        # Get sample data
        conversations = get_all_sample_conversations()
        
        # Run traditional review
        traditional_results = self._run_traditional_review(conversations)
        
        # Run memory enhanced review
        memory_results = self._run_memory_enhanced_review(conversations)
        
        # Compare results
        comparison = self._compare_results(traditional_results, memory_results)
        
        # Generate report
        report = self._generate_experiment_report(
            traditional_results, memory_results, comparison
        )
        
        self.logger.info("Full experiment completed")
        return report
    
    def _run_traditional_review(self, conversations: List[Dict]) -> Dict[str, Any]:
        """运行传统审查方法"""
        self.logger.info("Running traditional review")
        
        results = {
            "method": "traditional",
            "total_cases": len(conversations),
            "case_results": {},
            "performance_metrics": {
                "total_time": 0.0,
                "avg_time_per_case": 0.0,
                "total_tokens": 0,
                "avg_tokens_per_case": 0
            }
        }
        
        total_start_time = time.time()
        
        for conv in conversations:
            case_id = conv["case_id"]
            messages = conv["messages"]
            
            self.logger.info(f"Processing case {case_id} with traditional method")
            
            case_start_time = time.time()
            
            try:
                # Run traditional review
                result = self.traditional_reviewer.run_sync_with_chunking(messages, case_id)
                case_time = time.time() - case_start_time
                
                # Estimate token usage (simplified)
                formatted_content = "\n".join([
                    f"<|im_start|>{m['type']}\n{m['msg']}<|im_end|>" for m in messages
                ])
                from dc_ai_red_line_review.utils import get_token_count
                tokens = get_token_count(formatted_content)
                
                results["case_results"][case_id] = {
                    "review_result": result.get("review_res", {}),
                    "processing_time": case_time,
                    "token_usage": tokens,
                    "success": True
                }
                
                results["performance_metrics"]["total_tokens"] += tokens
                
            except Exception as e:
                self.logger.error(f"Traditional review failed for case {case_id}: {e}")
                results["case_results"][case_id] = {
                    "review_result": {},
                    "processing_time": time.time() - case_start_time,
                    "token_usage": 0,
                    "success": False,
                    "error": str(e)
                }
        
        total_time = time.time() - total_start_time
        results["performance_metrics"]["total_time"] = total_time
        results["performance_metrics"]["avg_time_per_case"] = total_time / len(conversations)
        results["performance_metrics"]["avg_tokens_per_case"] = (
            results["performance_metrics"]["total_tokens"] / len(conversations)
        )
        
        return results
    
    def _run_memory_enhanced_review(self, conversations: List[Dict]) -> Dict[str, Any]:
        """运行记忆增强审查方法"""
        self.logger.info("Running memory enhanced review")
        
        results = {
            "method": "memory_enhanced",
            "total_cases": len(conversations),
            "case_results": {},
            "performance_metrics": {
                "total_time": 0.0,
                "avg_time_per_case": 0.0,
                "total_tokens": 0,
                "avg_tokens_per_case": 0,
                "memory_hits": 0,
                "compression_ratio": 0.0
            }
        }
        
        total_start_time = time.time()
        total_compression_ratio = 0.0
        
        for conv in conversations:
            case_id = conv["case_id"]
            messages = conv["messages"]
            user_id = conv.get("user_id")
            agent_id = conv.get("agent_id")
            
            self.logger.info(f"Processing case {case_id} with memory enhanced method")
            
            case_start_time = time.time()
            
            try:
                # Run memory enhanced review
                result = self.memory_reviewer.enhanced_review(
                    messages, case_id, user_id=user_id, agent_id=agent_id
                )
                case_time = time.time() - case_start_time
                
                # Extract metrics
                memory_insights = result.get("memory_insights", {})
                review_result = result.get("review_res", {})
                
                # Estimate token usage (may be reduced due to compression)
                formatted_content = "\n".join([
                    f"<|im_start|>{m['type']}\n{m['msg']}<|im_end|>" for m in messages
                ])
                from dc_ai_red_line_review.utils import get_token_count
                original_tokens = get_token_count(formatted_content)
                
                # Simulate token reduction based on strategy
                strategy = memory_insights.get("review_strategy", {}).get("strategy", "full_review")
                if strategy == "memory_assisted":
                    actual_tokens = int(original_tokens * 0.7)  # 30% reduction
                elif strategy == "quick_check":
                    actual_tokens = int(original_tokens * 0.4)  # 60% reduction
                else:
                    actual_tokens = original_tokens
                
                compression_ratio = 1.0 - (actual_tokens / original_tokens) if original_tokens > 0 else 0.0
                total_compression_ratio += compression_ratio
                
                results["case_results"][case_id] = {
                    "review_result": review_result,
                    "memory_insights": memory_insights,
                    "processing_time": case_time,
                    "original_tokens": original_tokens,
                    "actual_tokens": actual_tokens,
                    "compression_ratio": compression_ratio,
                    "success": True
                }
                
                results["performance_metrics"]["total_tokens"] += actual_tokens
                
                # Count memory hits
                if memory_insights.get("relevant_memories_count", 0) > 0:
                    results["performance_metrics"]["memory_hits"] += 1
                
            except Exception as e:
                self.logger.error(f"Memory enhanced review failed for case {case_id}: {e}")
                results["case_results"][case_id] = {
                    "review_result": {},
                    "memory_insights": {},
                    "processing_time": time.time() - case_start_time,
                    "original_tokens": 0,
                    "actual_tokens": 0,
                    "compression_ratio": 0.0,
                    "success": False,
                    "error": str(e)
                }
        
        total_time = time.time() - total_start_time
        results["performance_metrics"]["total_time"] = total_time
        results["performance_metrics"]["avg_time_per_case"] = total_time / len(conversations)
        results["performance_metrics"]["avg_tokens_per_case"] = (
            results["performance_metrics"]["total_tokens"] / len(conversations)
        )
        results["performance_metrics"]["compression_ratio"] = (
            total_compression_ratio / len(conversations)
        )
        
        return results
    
    def _compare_results(self, traditional: Dict, memory_enhanced: Dict) -> Dict[str, Any]:
        """对比两种方法的结果"""
        self.logger.info("Comparing results")
        
        comparison = {
            "performance_comparison": {},
            "accuracy_comparison": {},
            "case_by_case_comparison": {}
        }
        
        # Performance comparison
        trad_metrics = traditional["performance_metrics"]
        mem_metrics = memory_enhanced["performance_metrics"]
        
        comparison["performance_comparison"] = {
            "time_improvement": {
                "traditional_avg": trad_metrics["avg_time_per_case"],
                "memory_enhanced_avg": mem_metrics["avg_time_per_case"],
                "improvement_ratio": (
                    (trad_metrics["avg_time_per_case"] - mem_metrics["avg_time_per_case"]) 
                    / trad_metrics["avg_time_per_case"]
                ) if trad_metrics["avg_time_per_case"] > 0 else 0.0
            },
            "token_reduction": {
                "traditional_avg": trad_metrics["avg_tokens_per_case"],
                "memory_enhanced_avg": mem_metrics["avg_tokens_per_case"],
                "reduction_ratio": (
                    (trad_metrics["avg_tokens_per_case"] - mem_metrics["avg_tokens_per_case"]) 
                    / trad_metrics["avg_tokens_per_case"]
                ) if trad_metrics["avg_tokens_per_case"] > 0 else 0.0
            },
            "memory_utilization": {
                "memory_hits": mem_metrics["memory_hits"],
                "total_cases": memory_enhanced["total_cases"],
                "hit_rate": mem_metrics["memory_hits"] / memory_enhanced["total_cases"]
            },
            "compression_effectiveness": {
                "avg_compression_ratio": mem_metrics["compression_ratio"]
            }
        }
        
        # Case-by-case comparison
        for case_id in traditional["case_results"]:
            if case_id in memory_enhanced["case_results"]:
                trad_case = traditional["case_results"][case_id]
                mem_case = memory_enhanced["case_results"][case_id]
                
                comparison["case_by_case_comparison"][case_id] = {
                    "traditional_success": trad_case["success"],
                    "memory_enhanced_success": mem_case["success"],
                    "time_difference": mem_case["processing_time"] - trad_case["processing_time"],
                    "token_difference": mem_case.get("actual_tokens", 0) - trad_case["token_usage"],
                    "strategy_used": mem_case.get("memory_insights", {}).get("review_strategy", {}).get("strategy", "unknown")
                }
        
        return comparison
    
    def _generate_experiment_report(self, 
                                  traditional: Dict, 
                                  memory_enhanced: Dict, 
                                  comparison: Dict) -> Dict[str, Any]:
        """生成实验报告"""
        self.logger.info("Generating experiment report")
        
        report = {
            "experiment_metadata": {
                "timestamp": datetime.now().isoformat(),
                "config": self.config.to_dict(),
                "total_cases": traditional["total_cases"]
            },
            "traditional_results": traditional,
            "memory_enhanced_results": memory_enhanced,
            "comparison": comparison,
            "summary": {},
            "recommendations": []
        }
        
        # Generate summary
        perf_comp = comparison["performance_comparison"]
        
        report["summary"] = {
            "time_improvement_percentage": perf_comp["time_improvement"]["improvement_ratio"] * 100,
            "token_reduction_percentage": perf_comp["token_reduction"]["reduction_ratio"] * 100,
            "memory_hit_rate": perf_comp["memory_utilization"]["hit_rate"] * 100,
            "avg_compression_ratio": perf_comp["compression_effectiveness"]["avg_compression_ratio"] * 100,
            "successful_cases_traditional": sum(
                1 for case in traditional["case_results"].values() if case["success"]
            ),
            "successful_cases_memory": sum(
                1 for case in memory_enhanced["case_results"].values() if case["success"]
            )
        }
        
        # Generate recommendations
        recommendations = []
        
        if perf_comp["time_improvement"]["improvement_ratio"] > 0.1:
            recommendations.append("Memory enhanced review shows significant time improvement (>10%)")
        
        if perf_comp["token_reduction"]["reduction_ratio"] > 0.2:
            recommendations.append("Memory enhanced review achieves substantial token reduction (>20%)")
        
        if perf_comp["memory_utilization"]["hit_rate"] > 0.5:
            recommendations.append("High memory hit rate indicates effective memory utilization")
        
        if perf_comp["compression_effectiveness"]["avg_compression_ratio"] > 0.3:
            recommendations.append("Effective content compression achieved")
        
        if not recommendations:
            recommendations.append("Consider tuning memory configuration for better performance")
        
        report["recommendations"] = recommendations
        
        return report
    
    def save_experiment_results(self, report: Dict[str, Any], filename: str = None):
        """保存实验结果"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"memory_experiment_report_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"Experiment results saved to {filename}")
            
        except Exception as e:
            self.logger.error(f"Failed to save experiment results: {e}")
    
    def print_summary(self, report: Dict[str, Any]):
        """打印实验摘要"""
        print("\n" + "="*60)
        print("MEMORY ENHANCED REVIEW EXPERIMENT SUMMARY")
        print("="*60)
        
        summary = report["summary"]
        
        print(f"Total Cases Processed: {report['experiment_metadata']['total_cases']}")
        print(f"Experiment Time: {report['experiment_metadata']['timestamp']}")
        print()
        
        print("PERFORMANCE IMPROVEMENTS:")
        print(f"  Time Improvement: {summary['time_improvement_percentage']:.1f}%")
        print(f"  Token Reduction: {summary['token_reduction_percentage']:.1f}%")
        print(f"  Memory Hit Rate: {summary['memory_hit_rate']:.1f}%")
        print(f"  Avg Compression: {summary['avg_compression_ratio']:.1f}%")
        print()
        
        print("SUCCESS RATES:")
        print(f"  Traditional Method: {summary['successful_cases_traditional']}/{report['experiment_metadata']['total_cases']}")
        print(f"  Memory Enhanced: {summary['successful_cases_memory']}/{report['experiment_metadata']['total_cases']}")
        print()
        
        print("RECOMMENDATIONS:")
        for i, rec in enumerate(report["recommendations"], 1):
            print(f"  {i}. {rec}")
        
        print("="*60)


def run_quick_experiment():
    """运行快速实验"""
    print("Starting Memory Enhanced Review Experiment...")
    
    # Initialize experiment
    config = MemoryConfig.from_env()
    experiment = ReviewExperiment(config)
    
    # Run experiment
    report = experiment.run_full_experiment()
    
    # Print summary
    experiment.print_summary(report)
    
    # Save results
    experiment.save_experiment_results(report)
    
    return report


if __name__ == "__main__":
    run_quick_experiment()
