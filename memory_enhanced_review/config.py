"""
Memory Enhanced Review Configuration
"""

import os
from dataclasses import dataclass
from typing import Dict, Any, Optional


@dataclass
class MemoryConfig:
    """Memory system configuration"""
    
    # Mem0 configuration
    mem0_api_key: Optional[str] = None
    mem0_base_url: Optional[str] = None
    
    # Memory storage settings
    memory_ttl: int = 7 * 24 * 3600  # 7 days in seconds
    max_memories_per_user: int = 100
    memory_similarity_threshold: float = 0.8
    
    # Review enhancement settings
    enable_memory_compression: bool = True
    enable_pattern_learning: bool = True
    enable_risk_scoring: bool = True
    
    # Token optimization
    max_context_tokens: int = 4000
    memory_compression_ratio: float = 0.3  # Target compression ratio
    
    @classmethod
    def from_env(cls) -> "MemoryConfig":
        """Create config from environment variables"""
        return cls(
            mem0_api_key=os.getenv("MEM0_API_KEY"),
            mem0_base_url=os.getenv("MEM0_BASE_URL", "https://api.mem0.ai"),
            memory_ttl=int(os.getenv("MEMORY_TTL", 7 * 24 * 3600)),
            max_memories_per_user=int(os.getenv("MAX_MEMORIES_PER_USER", 100)),
            memory_similarity_threshold=float(os.getenv("MEMORY_SIMILARITY_THRESHOLD", 0.8)),
            enable_memory_compression=os.getenv("ENABLE_MEMORY_COMPRESSION", "true").lower() == "true",
            enable_pattern_learning=os.getenv("ENABLE_PATTERN_LEARNING", "true").lower() == "true",
            enable_risk_scoring=os.getenv("ENABLE_RISK_SCORING", "true").lower() == "true",
            max_context_tokens=int(os.getenv("MAX_CONTEXT_TOKENS", 4000)),
            memory_compression_ratio=float(os.getenv("MEMORY_COMPRESSION_RATIO", 0.3))
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "mem0_api_key": self.mem0_api_key,
            "mem0_base_url": self.mem0_base_url,
            "memory_ttl": self.memory_ttl,
            "max_memories_per_user": self.max_memories_per_user,
            "memory_similarity_threshold": self.memory_similarity_threshold,
            "enable_memory_compression": self.enable_memory_compression,
            "enable_pattern_learning": self.enable_pattern_learning,
            "enable_risk_scoring": self.enable_risk_scoring,
            "max_context_tokens": self.max_context_tokens,
            "memory_compression_ratio": self.memory_compression_ratio
        }


# Default configuration
DEFAULT_CONFIG = MemoryConfig(
    memory_ttl=7 * 24 * 3600,  # 7 days
    max_memories_per_user=100,
    memory_similarity_threshold=0.8,
    enable_memory_compression=True,
    enable_pattern_learning=True,
    enable_risk_scoring=True,
    max_context_tokens=4000,
    memory_compression_ratio=0.3
)
