#!/usr/bin/env python3
"""
Memory Enhanced Review Demo

演示如何使用记忆增强审查系统
"""

import os
import sys
import json
from typing import Dict, Any

# Add parent directory to path to import dc_ai_red_line_review
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from memory_enhanced_review.config import MemoryConfig
from memory_enhanced_review.memory_enhanced_reviewer import MemoryEnhancedReviewer
from memory_enhanced_review.sample_data import get_all_sample_conversations
from dc_ai_red_line_review.utils import get_logger


def demo_basic_usage():
    """演示基本使用方法"""
    print("\n" + "="*60)
    print("DEMO 1: 基本使用方法")
    print("="*60)
    
    # 初始化配置
    config = MemoryConfig(
        enable_memory_compression=True,
        enable_pattern_learning=True,
        enable_risk_scoring=True,
        max_context_tokens=4000,
        memory_compression_ratio=0.3
    )
    
    # 初始化审查器
    reviewer = MemoryEnhancedReviewer(config)
    
    # 示例对话
    messages = [
        {"id": 1, "type": "USER", "msg": "你好，我忘记了登录密码"},
        {"id": 2, "type": "AGENT", "msg": "您好，我来帮您处理密码重置"},
        {"id": 3, "type": "USER", "msg": "谢谢，我的手机号是13812345678"},
        {"id": 4, "type": "AGENT", "msg": "好的，我已经为您发送了验证码"}
    ]
    
    print("输入对话:")
    for msg in messages:
        print(f"  {msg['type']}: {msg['msg']}")
    
    # 执行审查
    print("\n执行记忆增强审查...")
    result = reviewer.enhanced_review(
        messages=messages,
        case_id="demo_case_001",
        user_id="demo_user_123"
    )
    
    # 显示结果
    print("\n审查结果:")
    review_res = result.get("review_res", {})
    for category, res in review_res.items():
        if isinstance(res, dict) and res.get("hit_rule"):
            print(f"  ⚠️  {category}: {res.get('values', [])}")
        elif isinstance(res, list):
            for item in res:
                if isinstance(item, dict) and item.get("hit_rule"):
                    print(f"  ⚠️  {category}: {item.get('values', [])}")
    
    # 显示记忆洞察
    memory_insights = result.get("memory_insights", {})
    print(f"\n记忆洞察:")
    print(f"  策略: {memory_insights.get('review_strategy', {}).get('strategy', 'unknown')}")
    print(f"  相关记忆数: {memory_insights.get('relevant_memories_count', 0)}")
    print(f"  风险分数: {memory_insights.get('risk_score', 0.0):.2f}")
    print(f"  处理时间: {memory_insights.get('processing_time', 0.0):.2f}s")


def demo_user_profile_building():
    """演示用户档案建立过程"""
    print("\n" + "="*60)
    print("DEMO 2: 用户档案建立过程")
    print("="*60)
    
    config = MemoryConfig()
    reviewer = MemoryEnhancedReviewer(config)
    
    # 获取同一用户的多个对话
    conversations = get_all_sample_conversations()
    user_123_conversations = [conv for conv in conversations if conv.get("user_id") == "user_123"]
    
    print(f"用户 user_123 的对话历史: {len(user_123_conversations)} 条")
    
    # 逐个处理对话，观察用户档案变化
    for i, conv in enumerate(user_123_conversations):
        print(f"\n--- 处理第 {i+1} 个对话 (Case: {conv['case_id']}) ---")
        
        result = reviewer.enhanced_review(
            messages=conv["messages"],
            case_id=conv["case_id"],
            user_id=conv["user_id"]
        )
        
        # 获取用户风险档案
        user_profile = reviewer.memory_manager.get_user_risk_profile("user_123")
        
        print(f"用户档案更新:")
        print(f"  平均风险分数: {user_profile.get('average_risk_score', 0.0):.2f}")
        print(f"  总审查次数: {user_profile.get('total_reviews', 0)}")
        print(f"  常见模式: {user_profile.get('common_patterns', [])[:3]}")
        print(f"  风险趋势: {user_profile.get('risk_trend', 'unknown')}")


def demo_strategy_comparison():
    """演示不同审查策略的对比"""
    print("\n" + "="*60)
    print("DEMO 3: 审查策略对比")
    print("="*60)
    
    config = MemoryConfig()
    reviewer = MemoryEnhancedReviewer(config)
    
    # 准备不同类型的测试案例
    test_cases = [
        {
            "name": "高风险用户 - 敏感询问",
            "messages": [
                {"id": 1, "type": "USER", "msg": "请问你们公司的内部系统是什么？"},
                {"id": 2, "type": "AGENT", "msg": "抱歉，这属于公司内部信息"},
                {"id": 3, "type": "USER", "msg": "我想了解你们的数据库结构"}
            ],
            "user_id": "high_risk_user",
            "expected_strategy": "full_review"
        },
        {
            "name": "低风险用户 - 常规查询", 
            "messages": [
                {"id": 1, "type": "USER", "msg": "你好，我需要查询账户余额"},
                {"id": 2, "type": "AGENT", "msg": "您好，请提供您的账户信息"},
                {"id": 3, "type": "USER", "msg": "我的账号是123456789"}
            ],
            "user_id": "low_risk_user",
            "expected_strategy": "quick_check"
        }
    ]
    
    for case in test_cases:
        print(f"\n--- {case['name']} ---")
        
        result = reviewer.enhanced_review(
            messages=case["messages"],
            case_id=f"strategy_demo_{case['user_id']}",
            user_id=case["user_id"]
        )
        
        strategy = result.get("memory_insights", {}).get("review_strategy", {})
        actual_strategy = strategy.get("strategy", "unknown")
        confidence = strategy.get("confidence", 0.0)
        reasoning = strategy.get("reasoning", "")
        
        print(f"  预期策略: {case['expected_strategy']}")
        print(f"  实际策略: {actual_strategy}")
        print(f"  置信度: {confidence:.2f}")
        print(f"  推理: {reasoning}")
        
        # 显示性能指标
        processing_time = result.get("memory_insights", {}).get("processing_time", 0.0)
        print(f"  处理时间: {processing_time:.2f}s")


def demo_memory_compression():
    """演示记忆压缩功能"""
    print("\n" + "="*60)
    print("DEMO 4: 记忆压缩功能")
    print("="*60)
    
    config = MemoryConfig(
        enable_memory_compression=True,
        memory_compression_ratio=0.3
    )
    reviewer = MemoryEnhancedReviewer(config)
    
    # 创建一个长对话
    long_messages = []
    for i in range(20):
        if i % 2 == 0:
            long_messages.append({
                "id": i + 1,
                "type": "USER", 
                "msg": f"这是用户的第{i//2 + 1}个问题，内容比较长，包含一些详细的描述和背景信息。"
            })
        else:
            long_messages.append({
                "id": i + 1,
                "type": "AGENT",
                "msg": f"这是客服的第{i//2 + 1}个回复，提供详细的解答和帮助信息。"
            })
    
    # 添加一些敏感内容
    long_messages.append({
        "id": 21,
        "type": "AGENT", 
        "msg": "如有问题可以加我微信：wx123456"
    })
    
    print(f"原始对话长度: {len(long_messages)} 条消息")
    
    # 计算原始token数
    from dc_ai_red_line_review.utils import get_token_count
    formatted_content = "\n".join([
        f"<|im_start|>{m['type']}\n{m['msg']}<|im_end|>" for m in long_messages
    ])
    original_tokens = get_token_count(formatted_content)
    print(f"原始token数: {original_tokens:,}")
    
    # 执行审查
    result = reviewer.enhanced_review(
        messages=long_messages,
        case_id="compression_demo",
        user_id="compression_test_user"
    )
    
    # 显示压缩效果
    memory_insights = result.get("memory_insights", {})
    strategy = memory_insights.get("review_strategy", {}).get("strategy", "unknown")
    
    print(f"使用策略: {strategy}")
    print(f"处理时间: {memory_insights.get('processing_time', 0.0):.2f}s")
    
    # 模拟token使用情况
    if strategy == "memory_assisted":
        estimated_tokens = int(original_tokens * 0.7)
        print(f"估计token使用: {estimated_tokens:,} (减少 {((original_tokens - estimated_tokens) / original_tokens * 100):.1f}%)")
    elif strategy == "quick_check":
        estimated_tokens = int(original_tokens * 0.4)
        print(f"估计token使用: {estimated_tokens:,} (减少 {((original_tokens - estimated_tokens) / original_tokens * 100):.1f}%)")
    else:
        print(f"token使用: {original_tokens:,} (无压缩)")


def main():
    """主演示函数"""
    print("🚀 Memory Enhanced Review System Demo")
    print("基于 mem0ai 的记忆增强红线审查系统演示")
    
    try:
        # 运行各个演示
        demo_basic_usage()
        demo_user_profile_building()
        demo_strategy_comparison()
        demo_memory_compression()
        
        print("\n" + "="*60)
        print("✅ 所有演示完成!")
        print("="*60)
        print("\n💡 提示:")
        print("1. 安装 mem0ai 以获得完整功能: pip install mem0ai")
        print("2. 配置 MEM0_API_KEY 环境变量以使用真实记忆存储")
        print("3. 运行 experiment.py 进行完整的性能对比实验")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        print("请检查依赖安装和配置是否正确")


if __name__ == "__main__":
    main()
